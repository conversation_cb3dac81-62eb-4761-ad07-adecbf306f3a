# 📚 Chrome 阅读扩展文档中心

> 🎯 **技术栈**: React 18 + TypeScript 5 + Tailwind CSS 4 + Shadcn/UI  
> 📅 **最后更新**: 2024年12月  
> 🚀 **版本**: v3.0 (Shadcn/UI)

---

## 🗂️ 文档导航

### 🚀 **快速开始**
- [**快速开始指南**](./QUICK_START.md) - 5分钟快速上手开发环境

### 📖 **核心文档**
- [**开发指南**](./DEVELOPMENT_GUIDE.md) - 完整的开发指导文档
- [**API 参考**](./API_REFERENCE.md) - 完整的 API 参考文档
- [**设计系统规范**](./design-system-spec.md) - Shadcn/UI 设计系统规范

### 🔄 **迁移相关**
- [**迁移指南**](./MIGRATION_GUIDE.md) - 从 MD3 到 Shadcn/UI 的迁移指南
- [**UI 设计统一性总结**](./UI_DESIGN_SUMMARY.md) - UI 设计理念和实现
- [**Popup 重构规范**](./popup-redesign-spec.md) - Popup 界面设计规范

### 🛠️ **开发支持**
- [**测试指南**](./TESTING.md) - 完整的测试策略和工具
- [**性能优化指南**](./PERFORMANCE.md) - 性能优化最佳实践
- [**故障排除指南**](./TROUBLESHOOTING.md) - 常见问题诊断和解决

---

## 🎯 **技术栈概览**

### **核心技术**
| 技术 | 版本 | 用途 |
|------|------|------|
| **React** | ^18.2.0 | UI 框架 |
| **TypeScript** | ^5.2.2 | 类型安全 |
| **Vite** | ^5.1.6 | 构建工具 |
| **Tailwind CSS** | ^4.x | CSS 框架 |
| **Shadcn/UI** | latest | 组件库 |

### **UI 组件生态**
| 组件 | 技术栈 | 说明 |
|------|--------|------|
| **基础组件** | Radix UI | 无障碍的底层组件 |
| **图标系统** | Lucide React | 现代化图标库 |
| **状态管理** | Zustand | 轻量级状态管理 |
| **内容提取** | @mozilla/readability | 智能内容提取 |

---

## 📋 **文档使用指南**

### **新手开发者**
1. 📖 阅读 [快速开始指南](./QUICK_START.md)
2. 🔧 参考 [开发指南](./DEVELOPMENT_GUIDE.md) 第1-4章
3. 🎨 了解 [设计系统规范](./design-system-spec.md)

### **经验开发者**
1. 📚 查阅 [API 参考](./API_REFERENCE.md)
2. ⚡ 参考 [性能优化指南](./PERFORMANCE.md)
3. 🧪 使用 [测试指南](./TESTING.md)

### **设计师**
1. 🎨 查看 [设计系统规范](./design-system-spec.md)
2. 🔄 了解 [UI 设计统一性总结](./UI_DESIGN_SUMMARY.md)
3. 📱 参考 [Popup 重构规范](./popup-redesign-spec.md)

### **项目维护者**
1. 🔄 使用 [迁移指南](./MIGRATION_GUIDE.md)
2. 🛠️ 参考 [故障排除指南](./TROUBLESHOOTING.md)
3. 📊 监控 [性能优化指南](./PERFORMANCE.md) 中的指标

---

## 🔗 **文档关联图**

```
📚 文档中心 (README.md)
├── 🚀 快速开始
│   └── QUICK_START.md
├── 📖 核心开发
│   ├── DEVELOPMENT_GUIDE.md
│   ├── API_REFERENCE.md
│   └── design-system-spec.md
├── 🔄 迁移升级
│   ├── MIGRATION_GUIDE.md
│   ├── UI_DESIGN_SUMMARY.md
│   └── popup-redesign-spec.md
└── 🛠️ 开发支持
    ├── TESTING.md
    ├── PERFORMANCE.md
    └── TROUBLESHOOTING.md
```

---

## 📝 **文档维护**

### **更新原则**
- ✅ **技术栈一致性**: 所有文档必须反映当前的 Tailwind CSS 4 + Shadcn/UI 技术栈
- ✅ **实用性优先**: 专注于实际开发需要的信息
- ✅ **及时更新**: 代码变更后及时更新相关文档
- ✅ **交叉引用**: 保持文档间的逻辑关联和准确引用

### **贡献指南**
1. 📝 更新文档时请同步更新相关的交叉引用
2. 🎯 确保新增内容符合当前技术栈
3. 📋 重大变更请更新此文档索引
4. ✅ 提交前请检查文档格式和链接有效性

---

## 🎉 **版本历史**

- **v3.0** (2024-12) - 迁移到 Tailwind CSS 4 + Shadcn/UI
- **v2.0** (2024-11) - Material Design 3 完整实现
- **v1.0** (2024-10) - 初始版本

---

**📞 需要帮助？** 请查阅 [故障排除指南](./TROUBLESHOOTING.md) 或参考相关的专项文档。
