# Chrome扩展Popup页面重构设计规范

> 🎨 **技术栈**: Tailwind CSS 4 + Shadcn/UI
> 📅 **更新时间**: 2024年12月
> 🎯 **设计目标**: 基于Shadcn/UI的现代化组件设计

## 设计目标
采用 **Shadcn/UI 组件系统**，结合 Tailwind CSS 4，提供现代化、无障碍的用户体验。

## 布局结构设计

### 整体尺寸
- 宽度：380px（比现有360px稍宽，提供更好的内容展示空间）
- 高度：520px（比现有480px稍高，容纳更多设置项）
- 圆角：16px（现代化圆角设计）

### 布局分区

#### 1. 头部区域 (Header) - 高度80px
```
┌─────────────────────────────────────┐
│  🔖 阅读助手              🌙 ⚙️    │
│  优化网页阅读体验                    │
└─────────────────────────────────────┘
```
- 左侧：应用图标 + 标题 + 副标题
- 右侧：主题切换按钮 + 设置按钮
- 背景：渐变背景，增强视觉层次

#### 2. 阅读模式控制区域 - 高度80px
```
┌─────────────────────────────────────┐
│  📖 阅读模式              [开启]    │
│  提取主要内容，优化排版              │
└─────────────────────────────────────┘
```
- 大型卡片样式
- 左侧：状态描述
- 右侧：切换按钮
- 状态指示器

#### 3. 主要设置区域 - 高度300px（可滚动）
分为三个设置组，每组采用卡片式设计：

##### 3.1 字体设置卡片
```
┌─────────────────────────────────────┐
│  🔤 字体设置                        │
│  ┌─────────────────────────────────┐ │
│  │ 字体大小    [====●====] 18px   │ │
│  │ 字体类型    [默认字体 ▼]       │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

##### 3.2 排版设置卡片
```
┌─────────────────────────────────────┐
│  📐 排版设置                        │
│  ┌─────────────────────────────────┐ │
│  │ 行间距      [===●=====] 1.6    │ │
│  │ 段落间距    [==●======] 1.2    │ │
│  │ 文本对齐    [左对齐 ▼]         │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

##### 3.3 主题设置卡片
```
┌─────────────────────────────────────┐
│  🎨 主题设置                        │
│  ┌─────────────────────────────────┐ │
│  │ 背景颜色    [●] [○] [○] [○]    │ │
│  │ 页面宽度    [窄][中][宽][全屏]  │ │
│  └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

#### 4. 预设选择区域 - 高度120px
```
┌─────────────────────────────────────┐
│  📚 阅读预设                        │
│  ┌─────────┐ ┌─────────┐ ┌─────────┐ │
│  │ 📄 默认 │ │ 🌙 护眼 │ │ 🌃 深色 │ │
│  │   ●     │ │   ○     │ │   ○     │ │
│  └─────────┘ └─────────┘ └─────────┘ │
└─────────────────────────────────────┘
```

#### 5. 底部区域 (Footer) - 高度60px
```
┌─────────────────────────────────────┐
│  Chrome 阅读插件 v1.8.0    [更多设置] │
└─────────────────────────────────────┘
```

## 设计规范

### 卡片样式
- 背景：白色/深色模式自适应
- 圆角：12px
- 阴影：0 2px 8px rgba(0,0,0,0.1)
- 边框：1px solid rgba(0,0,0,0.08)
- 内边距：16px
- 卡片间距：12px

### 颜色系统
- 主色调：#4A90E2（蓝色）
- 辅助色：#7ED321（绿色）
- 背景色：#FFFFFF / #1A1A1A
- 文字色：#333333 / #FFFFFF
- 边框色：#E5E5E5 / #333333

### 交互状态
- 悬停：轻微阴影增强 + 2px上移
- 激活：轻微缩放(0.98)
- 禁用：透明度50%
- 过渡：200ms ease-in-out

### 响应式设计
- 支持不同屏幕密度
- 触摸友好的控件尺寸
- 键盘导航支持

## 信息架构优先级

### 第一优先级（始终可见）
1. 阅读模式开关
2. 字体大小调节
3. 预设选择

### 第二优先级（折叠/展开）
1. 字体类型选择
2. 行间距调节
3. 主题切换

### 第三优先级（高级设置）
1. 段落间距
2. 文本对齐
3. 页面宽度
4. 背景颜色选择

## 技术实现要点

### 组件选择
- 使用design-system中的MD3组件
- Card、Button、Slider等现有组件
- 需要扩展的组件：颜色选择器、预设卡片

### 状态管理
- 继续使用useSettingsStore
- 实时同步设置变更
- 优化性能，避免不必要的重渲染

### 动画效果
- 卡片展开/收起动画
- 设置变更的视觉反馈
- 加载状态动画

## 设置项详细分组方案

### 字体设置组 (Font Settings)
**优先级：高** - 用户最常调整的设置
- **字体大小 (fontSize)**: 滑块控件，范围12-24px，步长1px，默认18px
- **字体类型 (fontFamily)**: 下拉选择，选项：默认、宋体、黑体、楷体、苹方、微软雅黑
- **代码字体大小 (codeFontSize)**: 滑块控件，范围10-20px，步长1px，默认14px（高级设置）

### 排版设置组 (Typography Settings)
**优先级：中** - 影响阅读体验的重要设置
- **行间距 (lineHeight)**: 滑块控件，范围1.0-3.0，步长0.1，默认1.6
- **段落间距 (paragraphSpacing)**: 滑块控件，范围0.5-3.0，步长0.1，默认1.2
- **文本对齐 (textAlign)**: 按钮组，选项：左对齐、居中、右对齐、两端对齐
- **页面宽度 (pageWidth)**: 按钮组，选项：窄(600px)、中(800px)、宽(1000px)、全屏

### 主题设置组 (Theme Settings)
**优先级：中** - 视觉和舒适度设置
- **主题模式 (theme)**: 切换开关，浅色/深色
- **背景颜色 (backgroundColor)**: 颜色选择器，预设颜色：白色、暖色、冷色、护眼、奶油色、薄荷色、灰色
- **显示图片 (showImages)**: 切换开关，默认开启

### 代码设置组 (Code Settings)
**优先级：低** - 针对技术内容的专门设置（可折叠）
- **代码主题 (codeTheme)**: 下拉选择，选项：GitHub、One Dark、Dracula等
- **代码字体大小 (codeFontSize)**: 已包含在字体设置组中

### 预设管理组 (Preset Management)
**优先级：高** - 快速切换整套设置
- **内置预设**: 默认、护眼、深色、学术、编程
- **自定义预设**: 用户保存的个性化设置（未来功能）

## 布局优化方案

### 响应式折叠设计
```
基础视图（默认显示）:
┌─────────────────────────────────────┐
│ 🔖 阅读助手                🌙 ⚙️   │
│ 📖 阅读模式切换                     │
│ 🔤 字体设置 [展开]                  │
│   - 字体大小滑块                    │
│   - 字体类型选择                    │
│ 📐 排版设置 [收起]                  │
│ 🎨 主题设置 [收起]                  │
│ 📚 阅读预设                        │
└─────────────────────────────────────┘

展开视图（点击展开后）:
┌─────────────────────────────────────┐
│ 📐 排版设置 [收起]                  │
│   - 行间距滑块                      │
│   - 段落间距滑块                    │
│   - 文本对齐按钮组                  │
│   - 页面宽度按钮组                  │
└─────────────────────────────────────┘
```

### 智能显示逻辑
1. **默认显示**: 字体设置组始终展开，其他组收起
2. **记忆状态**: 记住用户的展开/收起偏好
3. **一次一组**: 同时只能展开一个设置组，避免界面过长
4. **快速访问**: 预设选择始终可见，提供快速切换

### 交互优化
1. **即时预览**: 设置变更立即在当前页面生效
2. **批量应用**: 预设切换时批量更新所有相关设置
3. **撤销功能**: 提供"恢复默认"选项
4. **设置同步**: 实时保存到Chrome存储

这个设计方案平衡了功能完整性和界面简洁性，确保用户能够快速访问最常用的设置，同时保持界面的现代化和易用性。
