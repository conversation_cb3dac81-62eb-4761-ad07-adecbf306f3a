{"name": "ai-reading-extension", "private": true, "version": "1.8.0", "type": "module", "scripts": {"dev": "vite", "build:content": "vite build -c vite.content.config.ts", "build:content-shadcn": "vite build -c vite.content-shadcn.config.ts", "build:background": "vite build -c vite.background.config.ts", "build:popup": "vite build -c vite.popup.config.ts", "cleanup": "node scripts/cleanup.js", "build": "pnpm run build:content-shadcn && pnpm run build:background && pnpm run build:popup && cp -r public/* dist/ && pnpm run cleanup", "lint": "eslint . --ext ts,tsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview", "watch": "vite build --watch"}, "dependencies": {"@mozilla/readability": "^0.5.0", "@radix-ui/react-dialog": "^1.1.2", "@radix-ui/react-dropdown-menu": "^2.1.2", "@radix-ui/react-slider": "^1.2.1", "@radix-ui/react-slot": "^1.2.3", "@radix-ui/react-switch": "^1.1.1", "@radix-ui/react-tooltip": "^1.1.3", "@tailwindcss/postcss": "^4.1.11", "@types/chrome": "^0.0.287", "@types/node": "^22.10.2", "autoprefixer": "^10.4.20", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "lucide-react": "^0.468.0", "postcss": "^8.4.49", "react": "^18.2.0", "react-dom": "^18.2.0", "tailwind-merge": "^3.1.0", "tailwindcss": "^4.0.0-beta.5", "turndown": "^7.2.0", "zustand": "^4.5.2"}, "devDependencies": {"@types/react": "^18.2.64", "@types/react-dom": "^18.2.21", "@types/turndown": "^5.0.5", "@typescript-eslint/eslint-plugin": "^7.1.1", "@typescript-eslint/parser": "^7.1.1", "@vitejs/plugin-react": "^4.2.1", "eslint": "^8.57.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "terser": "^5.43.1", "typescript": "^5.2.2", "vite": "^5.1.6"}}