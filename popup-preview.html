<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome阅读插件 Popup 配置整合预览</title>
    <style>
        /* Material Design 3 颜色变量模拟 */
        :root {
            --md-sys-color-primary: #6366f1;
            --md-sys-color-on-primary: #ffffff;
            --md-sys-color-primary-container: rgba(99, 102, 241, 0.1);
            --md-sys-color-on-primary-container: #1e1b4b;
            --md-sys-color-surface: #ffffff;
            --md-sys-color-on-surface: #1f2937;
            --md-sys-color-on-surface-variant: #6b7280;
            --md-sys-color-surface-variant: #f3f4f6;
            --md-sys-color-outline: #d1d5db;
            --md-sys-color-outline-variant: #e5e7eb;
        }

        body {
            margin: 0;
            padding: 40px;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', '<PERSON><PERSON>', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .preview-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 30px;
        }

        .preview-title {
            color: white;
            font-size: 28px;
            font-weight: 600;
            text-align: center;
            margin-bottom: 20px;
            text-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
        }

        .preview-subtitle {
            color: rgba(255, 255, 255, 0.9);
            font-size: 16px;
            text-align: center;
            margin-bottom: 30px;
            max-width: 500px;
            line-height: 1.5;
        }

        .popup-frame {
            background: white;
            border-radius: 24px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.3);
            overflow: hidden;
            position: relative;
        }

        /* 导入重新设计的CSS */
    </style>
    <link rel="stylesheet" href="src/popup/PopupMD3.css">
</head>
<body>
    <div class="preview-container">
        <h1 class="preview-title">🔧 Chrome阅读插件 配置整合方案</h1>
        <p class="preview-subtitle">
            解决"两层配置堆叠"问题：预设选择优先，高级设置可选，统一配置界面
        </p>
        
        <div class="popup-frame">
            <div class="popup-container">
                <!-- Header -->
                <div class="popup-header">
                    <div class="popup-header-content">
                        <div class="flex">
                            <div class="w-8">
                                <svg class="w-5 h-5 text-on-primary" fill="currentColor" viewBox="0 0 20 20" style="width: 20px; height: 20px; color: white;">
                                    <path d="M12 6.253v13m0-13C10.832 5.477 9.246 5 7.5 5S4.168 5.477 3 6.253v13C4.168 18.477 5.754 18 7.5 18s3.332.477 4.5 1.253m0-13C13.168 5.477 14.754 5 16.5 5c1.747 0 3.332.477 4.5 1.253v13C19.832 18.477 18.247 18 16.5 18c-1.746 0-3.332.477-4.5 1.253" />
                                </svg>
                            </div>
                            <div>
                                <h1>阅读助手</h1>
                                <p>优化网页阅读体验</p>
                            </div>
                        </div>
                        <div style="display: flex; align-items: center; gap: 8px;">
                            <button style="width: 32px; height: 32px; border: none; background: none; border-radius: 8px; cursor: pointer; display: flex; align-items: center; justify-content: center; font-size: 16px;">🌙</button>
                        </div>
                    </div>
                </div>

                <!-- Reading Mode Control -->
                <div class="reading-mode-card">
                    <div class="reading-mode-content">
                        <div class="flex">
                            <div style="display: flex; align-items: center; gap: 16px;">
                                <div class="text-2xl">📖</div>
                                <div>
                                    <h3>阅读模式</h3>
                                    <p>提取主要内容，优化排版</p>
                                </div>
                            </div>
                            <div class="custom-switch">
                                <div class="custom-switch-track large checked">
                                    <div class="custom-switch-thumb large checked"></div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Main Settings Area -->
                <div class="settings-area">
                    <!-- Quick Presets Section - 主要配置方式 -->
                    <div class="presets-section-primary">
                        <div class="presets-header">
                            <h3>
                                <span>⚡</span>
                                快速预设
                            </h3>
                            <p class="presets-description">推荐使用预设快速配置，或在下方进行详细调整</p>
                        </div>
                        
                        <div class="presets-grid">
                            <div class="preset-button selected">
                                <div class="preset-icon">📄</div>
                                <div class="preset-name">纸质书籍</div>
                                <div class="preset-indicator"></div>
                            </div>
                            <div class="preset-button">
                                <div class="preset-icon">🌃</div>
                                <div class="preset-name">夜间模式</div>
                                <div class="preset-indicator"></div>
                            </div>
                            <div class="preset-button">
                                <div class="preset-icon">🌙</div>
                                <div class="preset-name">护眼模式</div>
                                <div class="preset-indicator"></div>
                            </div>
                        </div>

                        <!-- 预设状态指示 -->
                        <div class="preset-status">
                            <div class="custom-status">
                                <span class="status-icon">✏️</span>
                                <span class="status-text">已自定义</span>
                                <button class="reset-preset-btn" title="重置为预设配置">重置</button>
                            </div>
                        </div>
                    </div>

                    <!-- Advanced Settings Toggle -->
                    <div class="advanced-settings-toggle">
                        <button class="advanced-toggle-btn" id="advancedToggle">
                            <div class="advanced-toggle-content">
                                <div class="flex">
                                    <span class="advanced-icon">⚙️</span>
                                    <div>
                                        <h4>高级设置</h4>
                                        <p>精细调整字体、排版和主题</p>
                                    </div>
                                </div>
                                <div class="advanced-expand-icon" id="advancedExpandIcon">▼</div>
                            </div>
                        </button>
                    </div>

                    <!-- Advanced Settings Area -->
                    <div class="advanced-settings-area" id="advancedSettings" style="display: none;">
                        <!-- Font Settings Card -->
                        <div class="settings-card">
                            <div class="settings-card-header">
                                <div class="settings-card-title">
                                    <div class="settings-card-icon">🔤</div>
                                    <h3>字体设置</h3>
                                </div>
                                <div class="settings-card-expand-icon expanded">▼</div>
                            </div>
                            <div class="settings-card-content">
                                <div class="setting-item">
                                    <div class="setting-label">
                                        <span class="setting-label-text">字体大小</span>
                                        <span class="setting-value">18px</span>
                                    </div>
                                    <div class="custom-slider">
                                        <div class="custom-slider-track">
                                            <div class="custom-slider-fill" style="width: 50%;"></div>
                                        </div>
                                        <div class="custom-slider-thumb" style="left: 50%;"></div>
                                    </div>
                                </div>
                                <div class="setting-item">
                                    <label class="setting-label-text" style="display: block; margin-bottom: 8px;">字体类型</label>
                                    <select class="custom-select">
                                        <option>系统默认</option>
                                        <option>微软雅黑</option>
                                        <option>苹方</option>
                                    </select>
                                </div>
                            </div>
                        </div>

                        <!-- Typography Settings Card -->
                        <div class="settings-card">
                            <div class="settings-card-header">
                                <div class="settings-card-title">
                                    <div class="settings-card-icon">📐</div>
                                    <h3>排版设置</h3>
                                </div>
                                <div class="settings-card-expand-icon">▼</div>
                            </div>
                        </div>

                        <!-- Theme Settings Card -->
                        <div class="settings-card">
                            <div class="settings-card-header">
                                <div class="settings-card-title">
                                    <div class="settings-card-icon">🎨</div>
                                    <h3>主题设置</h3>
                                </div>
                                <div class="settings-card-expand-icon">▼</div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Footer -->
                <div class="popup-footer">
                    <span class="footer-version">Chrome 阅读插件 v1.8.0</span>
                    <span class="footer-integration-note">统一配置界面</span>
                </div>
            </div>
        </div>

        <div style="color: rgba(255, 255, 255, 0.8); text-align: center; max-width: 700px; font-size: 14px; line-height: 1.6;">
            <p><strong>🎯 解决方案：方案1 - 统一配置界面</strong></p>
            <p>• <strong>预设优先</strong>：快速预设作为主要配置方式，突出显示<br>
            • <strong>智能交互</strong>：预设状态检测，修改后显示"已自定义"<br>
            • <strong>高级选项</strong>：详细设置作为可选的高级功能<br>
            • <strong>统一界面</strong>：无需跳转options页面，一站式配置体验</p>
        </div>
    </div>

    <script>
        // 简单的交互演示
        document.addEventListener('DOMContentLoaded', function() {
            // 高级设置展开/收起
            const advancedToggle = document.getElementById('advancedToggle');
            const advancedSettings = document.getElementById('advancedSettings');
            const advancedExpandIcon = document.getElementById('advancedExpandIcon');

            advancedToggle.addEventListener('click', function() {
                if (advancedSettings.style.display === 'none') {
                    advancedSettings.style.display = 'block';
                    advancedExpandIcon.classList.add('expanded');
                } else {
                    advancedSettings.style.display = 'none';
                    advancedExpandIcon.classList.remove('expanded');
                }
            });

            // 设置卡片展开/收起
            document.querySelectorAll('.settings-card-header').forEach(header => {
                header.addEventListener('click', function() {
                    const expandIcon = this.querySelector('.settings-card-expand-icon');
                    const content = this.parentElement.querySelector('.settings-card-content');
                    
                    if (expandIcon.classList.contains('expanded')) {
                        expandIcon.classList.remove('expanded');
                        if (content) content.style.display = 'none';
                    } else {
                        expandIcon.classList.add('expanded');
                        if (content) content.style.display = 'block';
                    }
                });
            });

            // 预设按钮选择
            document.querySelectorAll('.preset-button').forEach(button => {
                button.addEventListener('click', function() {
                    document.querySelectorAll('.preset-button').forEach(b => b.classList.remove('selected'));
                    this.classList.add('selected');
                });
            });

            // 重置按钮演示
            document.querySelector('.reset-preset-btn').addEventListener('click', function() {
                // 切换状态显示
                const customStatus = document.querySelector('.custom-status');
                const presetStatus = document.querySelector('.preset-status');
                
                customStatus.style.display = 'none';
                presetStatus.innerHTML = `
                    <div class="preset-status-normal">
                        <span class="status-icon">✅</span>
                        <span class="status-text">使用 "纸质书籍" 预设</span>
                    </div>
                `;
                
                // 3秒后恢复
                setTimeout(() => {
                    customStatus.style.display = 'flex';
                    presetStatus.innerHTML = '';
                    presetStatus.appendChild(customStatus);
                }, 3000);
            });
        });
    </script>
</body>
</html> 