@import "tailwindcss";

@theme {
  --color-background: 0 0% 100%;
  --color-foreground: 222.2 84% 4.9%;
  --color-card: 0 0% 100%;
  --color-card-foreground: 222.2 84% 4.9%;
  --color-popover: 0 0% 100%;
  --color-popover-foreground: 222.2 84% 4.9%;
  --color-primary: 221.2 83.2% 53.3%;
  --color-primary-foreground: 210 40% 98%;
  --color-secondary: 210 40% 96%;
  --color-secondary-foreground: 222.2 84% 4.9%;
  --color-muted: 210 40% 96%;
  --color-muted-foreground: 215.4 16.3% 46.9%;
  --color-accent: 210 40% 96%;
  --color-accent-foreground: 222.2 84% 4.9%;
  --color-destructive: 0 84.2% 60.2%;
  --color-destructive-foreground: 210 40% 98%;
  --color-border: 214.3 31.8% 91.4%;
  --color-input: 214.3 31.8% 91.4%;
  --color-ring: 221.2 83.2% 53.3%;
  --radius: 0.5rem;

  /* 保留阅读模式相关的颜色 */
  --color-paper-cream: #F8F5F1;
  --color-paper-mint: #f1f7f5;
  --color-paper-warm: #f9f3ee;
  --color-paper-cool: #f2f5f8;
  --color-paper-sepia: #f5f2e9;
}

@media (prefers-color-scheme: dark) {
  @theme {
    --color-background: 222.2 84% 4.9%;
    --color-foreground: 210 40% 98%;
    --color-card: 222.2 84% 4.9%;
    --color-card-foreground: 210 40% 98%;
    --color-popover: 222.2 84% 4.9%;
    --color-popover-foreground: 210 40% 98%;
    --color-primary: 217.2 91.2% 59.8%;
    --color-primary-foreground: 222.2 84% 4.9%;
    --color-secondary: 217.2 32.6% 17.5%;
    --color-secondary-foreground: 210 40% 98%;
    --color-muted: 217.2 32.6% 17.5%;
    --color-muted-foreground: 215 20.2% 65.1%;
    --color-accent: 217.2 32.6% 17.5%;
    --color-accent-foreground: 210 40% 98%;
    --color-destructive: 0 62.8% 30.6%;
    --color-destructive-foreground: 210 40% 98%;
    --color-border: 217.2 32.6% 17.5%;
    --color-input: 217.2 32.6% 17.5%;
    --color-ring: 224.3 76.3% 94.1%;
  }
}

* {
  border-color: hsl(var(--color-border));
}

body {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
}

/* Chrome Extension specific styles */
.extension-popup {
  min-width: 380px;
  min-height: 520px;
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
}

.extension-card {
  background-color: hsl(var(--color-card));
  color: hsl(var(--color-card-foreground));
  border-radius: 0.5rem;
  border: 1px solid hsl(var(--color-border));
  box-shadow: 0 1px 2px 0 rgb(0 0 0 / 0.05);
}

.reading-mode-container {
  background-color: hsl(var(--color-background));
  color: hsl(var(--color-foreground));
}

/* 保留阅读模式的特殊样式 */
.paper-cream {
  background-color: #F8F5F1;
  color: #2D1B0E;
}

.paper-mint {
  background-color: #f1f7f5;
  color: #1a3d32;
}

.paper-warm {
  background-color: #f9f3ee;
  color: #3d2e1f;
}

.paper-cool {
  background-color: #f2f5f8;
  color: #1f2937;
}

.paper-sepia {
  background-color: #f5f2e9;
  color: #4a3728;
}