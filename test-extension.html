<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Chrome阅读扩展测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            max-width: 1200px;
            margin: 0 auto;
            padding: 2rem;
            background: #f8f9fa;
        }
        
        .container {
            background: white;
            padding: 2rem;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 2rem;
        }
        
        h1, h2, h3 {
            color: #2c3e50;
        }
        
        .test-section {
            margin-bottom: 3rem;
            padding: 1.5rem;
            border: 1px solid #e9ecef;
            border-radius: 6px;
        }
        
        .code-block {
            background: #f8f9fa;
            padding: 1rem;
            border-radius: 4px;
            font-family: 'Monaco', '<PERSON><PERSON>', 'Ubuntu Mono', monospace;
            overflow-x: auto;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 0.2rem 0.4rem;
            border-radius: 3px;
        }
        
        .extension-info {
            background: #e7f3ff;
            padding: 1rem;
            border-radius: 6px;
            border-left: 4px solid #0066cc;
            margin: 1rem 0;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 0.75rem 1.5rem;
            border-radius: 4px;
            cursor: pointer;
            font-size: 1rem;
            margin: 0.5rem;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Chrome阅读扩展重构测试页面</h1>
        
        <div class="extension-info">
            <h3>📋 测试说明</h3>
            <p>本页面用于测试Chrome阅读扩展的新功能。请确保已安装最新版本的扩展程序。</p>
            <p><strong>版本：</strong> v1.8.0 (Shadcn/UI重构版)</p>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>🎨 界面测试</h2>
            <p>测试新的Shadcn/UI界面组件和Tailwind CSS 4样式系统。</p>
            
            <h3>Popup界面测试</h3>
            <ul>
                <li>点击扩展图标打开Popup界面</li>
                <li>验证新的现代化设计风格</li>
                <li>测试阅读模式开关</li>
                <li>测试快速预设功能</li>
                <li>测试高级设置面板</li>
            </ul>
            
            <button class="test-button" onclick="testPopup()">测试Popup界面</button>
        </div>

        <div class="test-section">
            <h2>📖 阅读模式测试</h2>
            <p>测试新的阅读模式功能，包括内容提取、样式应用和交互体验。</p>
            
            <h3>基础功能</h3>
            <ul>
                <li>点击浮动按钮或使用快捷键 <span class="highlight">Ctrl+R</span> 启用阅读模式</li>
                <li>验证内容提取的准确性</li>
                <li>测试阅读模式界面的响应性</li>
                <li>测试退出功能（ESC键或点击关闭按钮）</li>
            </ul>
            
            <h3>样式定制</h3>
            <ul>
                <li>字体大小调整（12px - 24px）</li>
                <li>行高调整（1.2 - 2.0）</li>
                <li>背景色切换（cream, mint, warm, cool, sepia）</li>
                <li>明暗主题切换</li>
            </ul>
            
            <button class="test-button" onclick="testReadingMode()">启用阅读模式</button>
        </div>

        <div class="test-section">
            <h2>⚡ 性能测试</h2>
            <p>验证新系统的性能表现和资源使用情况。</p>
            
            <h3>加载性能</h3>
            <ul>
                <li>扩展启动时间</li>
                <li>阅读模式激活速度</li>
                <li>样式切换响应时间</li>
                <li>内存使用情况</li>
            </ul>
            
            <h3>构建优化</h3>
            <ul>
                <li>JavaScript包大小：~204KB (gzip: ~66KB)</li>
                <li>CSS文件大小：~4KB (gzip: ~1KB)</li>
                <li>代码分割和懒加载</li>
                <li>Tree-shaking优化</li>
            </ul>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>📝 测试内容</h2>
            <p>以下是用于测试内容提取和阅读模式渲染的示例内容：</p>
            
            <h3>标题层级测试</h3>
            <h4>四级标题</h4>
            <h5>五级标题</h5>
            <h6>六级标题</h6>
            
            <h3>段落和文本格式</h3>
            <p>这是一个普通段落，用于测试文本渲染效果。段落包含了<strong>粗体文本</strong>、<em>斜体文本</em>和<span class="highlight">高亮文本</span>。</p>
            
            <p>这是另一个段落，包含了一个<a href="#" onclick="return false;">链接示例</a>。链接应该在阅读模式中正确显示和交互。</p>
            
            <h3>列表测试</h3>
            <ul>
                <li>无序列表项目1</li>
                <li>无序列表项目2
                    <ul>
                        <li>嵌套列表项目1</li>
                        <li>嵌套列表项目2</li>
                    </ul>
                </li>
                <li>无序列表项目3</li>
            </ul>
            
            <ol>
                <li>有序列表项目1</li>
                <li>有序列表项目2</li>
                <li>有序列表项目3</li>
            </ol>
            
            <h3>引用块测试</h3>
            <blockquote>
                这是一个引用块示例。引用块应该在阅读模式中有特殊的样式显示，包括左边框和不同的背景色。
            </blockquote>
            
            <h3>代码测试</h3>
            <p>这是一个内联代码示例：<code>console.log('Hello World')</code></p>
            
            <div class="code-block">
                <pre><code>// 这是一个代码块示例
function testFunction() {
    const message = 'Hello, Reading Mode!';
    console.log(message);
    return message;
}

testFunction();</code></pre>
            </div>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <h2>🔧 技术规格</h2>
            
            <h3>重构前后对比</h3>
            <table style="width: 100%; border-collapse: collapse; margin: 1rem 0;">
                <thead>
                    <tr style="background: #f8f9fa;">
                        <th style="padding: 0.75rem; border: 1px solid #dee2e6; text-align: left;">项目</th>
                        <th style="padding: 0.75rem; border: 1px solid #dee2e6; text-align: left;">重构前</th>
                        <th style="padding: 0.75rem; border: 1px solid #dee2e6; text-align: left;">重构后</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">CSS框架</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Tailwind CSS 3.4.17</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Tailwind CSS 4.1.11</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">组件库</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Material Design 3 (自定义)</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Shadcn/UI + Radix UI</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">图标系统</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Material Icons</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Lucide React</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">样式管理</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">CSS Modules + 自定义CSS</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">Tailwind CSS + CVA</td>
                    </tr>
                    <tr>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">包大小</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">~250KB</td>
                        <td style="padding: 0.75rem; border: 1px solid #dee2e6;">~204KB</td>
                    </tr>
                </tbody>
            </table>
        </div>
    </div>

    <script>
        function testPopup() {
            alert('请点击浏览器工具栏中的扩展图标来测试Popup界面！');
        }
        
        function testReadingMode() {
            alert('请使用快捷键 Ctrl+R 或点击页面上的浮动按钮来启用阅读模式！');
        }
        
        // 页面加载完成后的提示
        window.addEventListener('load', function() {
            console.log('🚀 Chrome阅读扩展测试页面已加载');
            console.log('📋 请按照页面说明进行功能测试');
        });
    </script>
</body>
</html>
